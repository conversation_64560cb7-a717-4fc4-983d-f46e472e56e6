package com.adins.mss.services.model.common;

import java.lang.reflect.Method;

import com.google.gson.Gson;

/**
 * Unit tests for AddTaskPoloRequest class focusing on new custProtectCode field
 * added between revisions 6d55721 and 367b8b2
 */
public class AddTaskPoloRequestTest {

    /**
     * Test custProtectCode getter method
     */
    public void testGetCustProtectCode() {
        System.out.println("Testing getCustProtectCode method...");
        
        try {
            AddTaskPoloRequest request = new AddTaskPoloRequest();
            
            // Test initial value (should be null)
            String initialValue = request.getCustProtectCode();
            if (initialValue == null) {
                System.out.println("✓ Initial custProtectCode value is null as expected");
            } else {
                System.err.println("✗ Initial custProtectCode value should be null, but was: " + initialValue);
            }
            
        } catch (Exception e) {
            System.err.println("✗ getCustProtectCode test failed: " + e.getMessage());
        }
    }

    /**
     * Test custProtectCode setter method
     */
    public void testSetCustProtectCode() {
        System.out.println("Testing setCustProtectCode method...");
        
        try {
            AddTaskPoloRequest request = new AddTaskPoloRequest();
            String testValue = "POLO_PROTECT001";
            
            // Set value
            request.setCustProtectCode(testValue);
            
            // Verify value was set
            String retrievedValue = request.getCustProtectCode();
            if (testValue.equals(retrievedValue)) {
                System.out.println("✓ setCustProtectCode correctly sets the value");
            } else {
                System.err.println("✗ setCustProtectCode failed. Expected: " + testValue + ", Got: " + retrievedValue);
            }
            
        } catch (Exception e) {
            System.err.println("✗ setCustProtectCode test failed: " + e.getMessage());
        }
    }

    /**
     * Test custProtectCode with null value
     */
    public void testSetCustProtectCode_NullValue() {
        System.out.println("Testing setCustProtectCode with null value...");
        
        try {
            AddTaskPoloRequest request = new AddTaskPoloRequest();
            
            // Set null value
            request.setCustProtectCode(null);
            
            // Verify null value was set
            String retrievedValue = request.getCustProtectCode();
            if (retrievedValue == null) {
                System.out.println("✓ setCustProtectCode correctly handles null value");
            } else {
                System.err.println("✗ setCustProtectCode with null failed. Expected: null, Got: " + retrievedValue);
            }
            
        } catch (Exception e) {
            System.err.println("✗ setCustProtectCode null test failed: " + e.getMessage());
        }
    }

    /**
     * Test custProtectCode with empty string
     */
    public void testSetCustProtectCode_EmptyString() {
        System.out.println("Testing setCustProtectCode with empty string...");
        
        try {
            AddTaskPoloRequest request = new AddTaskPoloRequest();
            String emptyValue = "";
            
            // Set empty value
            request.setCustProtectCode(emptyValue);
            
            // Verify empty value was set
            String retrievedValue = request.getCustProtectCode();
            if (emptyValue.equals(retrievedValue)) {
                System.out.println("✓ setCustProtectCode correctly handles empty string");
            } else {
                System.err.println("✗ setCustProtectCode with empty string failed. Expected: '', Got: " + retrievedValue);
            }
            
        } catch (Exception e) {
            System.err.println("✗ setCustProtectCode empty string test failed: " + e.getMessage());
        }
    }

    /**
     * Test JSON serialization of custProtectCode field
     */
    public void testCustProtectCode_JsonSerialization() {
        System.out.println("Testing custProtectCode JSON serialization...");
        
        try {
            AddTaskPoloRequest request = new AddTaskPoloRequest();
            request.setCustProtectCode("POLO_PROTECT123");
            
            Gson gson = new Gson();
            String json = gson.toJson(request);
            
            // Check if custProtectCode is present in JSON
            if (json.contains("custProtectCode") && json.contains("POLO_PROTECT123")) {
                System.out.println("✓ custProtectCode correctly serialized to JSON");
            } else {
                System.err.println("✗ custProtectCode not found in JSON serialization");
                System.err.println("JSON: " + json.substring(0, Math.min(200, json.length())) + "...");
            }
            
        } catch (Exception e) {
            System.err.println("✗ JSON serialization test failed: " + e.getMessage());
        }
    }

    /**
     * Test JSON deserialization of custProtectCode field
     */
    public void testCustProtectCode_JsonDeserialization() {
        System.out.println("Testing custProtectCode JSON deserialization...");
        
        try {
            String json = "{\"custProtectCode\":\"POLO_PROTECT456\"}";
            
            Gson gson = new Gson();
            AddTaskPoloRequest request = gson.fromJson(json, AddTaskPoloRequest.class);
            
            String deserializedValue = request.getCustProtectCode();
            if ("POLO_PROTECT456".equals(deserializedValue)) {
                System.out.println("✓ custProtectCode correctly deserialized from JSON");
            } else {
                System.err.println("✗ custProtectCode deserialization failed. Expected: POLO_PROTECT456, Got: " + deserializedValue);
            }
            
        } catch (Exception e) {
            System.err.println("✗ JSON deserialization test failed: " + e.getMessage());
        }
    }

    /**
     * Test JSON property annotation
     */
    public void testCustProtectCode_JsonPropertyAnnotation() {
        System.out.println("Testing custProtectCode JsonProperty annotation...");
        
        try {
            // Use reflection to check if the field has JsonProperty annotation
            java.lang.reflect.Field field = AddTaskPoloRequest.class.getDeclaredField("custProtectCode");
            
            if (field != null) {
                // Check if JsonProperty annotation exists
                boolean hasJsonProperty = field.isAnnotationPresent(com.fasterxml.jackson.annotation.JsonProperty.class);
                
                if (hasJsonProperty) {
                    com.fasterxml.jackson.annotation.JsonProperty annotation = 
                        field.getAnnotation(com.fasterxml.jackson.annotation.JsonProperty.class);
                    String propertyName = annotation.value();
                    
                    if ("custProtectCode".equals(propertyName)) {
                        System.out.println("✓ custProtectCode has correct JsonProperty annotation");
                    } else {
                        System.err.println("✗ JsonProperty annotation has wrong value: " + propertyName);
                    }
                } else {
                    System.err.println("✗ custProtectCode field missing JsonProperty annotation");
                }
            } else {
                System.err.println("✗ custProtectCode field not found");
            }
            
        } catch (Exception e) {
            System.err.println("✗ JsonProperty annotation test failed: " + e.getMessage());
        }
    }

    /**
     * Test method existence and accessibility
     */
    public void testMethodExistence() {
        System.out.println("Testing custProtectCode method existence...");
        
        try {
            Class<?> clazz = AddTaskPoloRequest.class;
            
            // Check getter method
            Method getter = clazz.getMethod("getCustProtectCode");
            if (getter != null && getter.getReturnType() == String.class) {
                System.out.println("✓ getCustProtectCode method exists with correct return type");
            } else {
                System.err.println("✗ getCustProtectCode method not found or wrong return type");
            }
            
            // Check setter method
            Method setter = clazz.getMethod("setCustProtectCode", String.class);
            if (setter != null && setter.getReturnType() == void.class) {
                System.out.println("✓ setCustProtectCode method exists with correct signature");
            } else {
                System.err.println("✗ setCustProtectCode method not found or wrong signature");
            }
            
        } catch (Exception e) {
            System.err.println("✗ Method existence test failed: " + e.getMessage());
        }
    }

    /**
     * Test field type consistency between AddTaskCAERequest and AddTaskPoloRequest
     */
    public void testFieldConsistency() {
        System.out.println("Testing custProtectCode field consistency...");
        
        try {
            // Both classes should have the same field type and behavior
            AddTaskPoloRequest poloRequest = new AddTaskPoloRequest();
            AddTaskCAERequest caeRequest = new AddTaskCAERequest();
            
            String testValue = "CONSISTENCY_TEST";
            
            poloRequest.setCustProtectCode(testValue);
            caeRequest.setCustProtectCode(testValue);
            
            String poloValue = poloRequest.getCustProtectCode();
            String caeValue = caeRequest.getCustProtectCode();
            
            if (testValue.equals(poloValue) && testValue.equals(caeValue)) {
                System.out.println("✓ custProtectCode field behaves consistently across both request types");
            } else {
                System.err.println("✗ Inconsistent behavior between AddTaskPoloRequest and AddTaskCAERequest");
            }
            
        } catch (Exception e) {
            System.err.println("✗ Field consistency test failed: " + e.getMessage());
        }
    }

    /**
     * Main method to run all tests
     */
    public static void main(String[] args) {
        AddTaskPoloRequestTest test = new AddTaskPoloRequestTest();
        
        System.out.println("=== AddTaskPoloRequest custProtectCode Test Suite ===");
        System.out.println("Testing new custProtectCode field added between revisions 6d55721 and 367b8b2\n");
        
        test.testGetCustProtectCode();
        test.testSetCustProtectCode();
        test.testSetCustProtectCode_NullValue();
        test.testSetCustProtectCode_EmptyString();
        test.testCustProtectCode_JsonSerialization();
        test.testCustProtectCode_JsonDeserialization();
        test.testCustProtectCode_JsonPropertyAnnotation();
        test.testMethodExistence();
        test.testFieldConsistency();
        
        System.out.println("\n=== AddTaskPoloRequest Test Suite Complete ===");
    }
}

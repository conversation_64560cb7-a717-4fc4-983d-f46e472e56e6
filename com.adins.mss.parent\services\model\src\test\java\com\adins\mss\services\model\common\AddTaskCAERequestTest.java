package com.adins.mss.services.model.common;

import java.lang.reflect.Method;

import com.google.gson.Gson;

/**
 * Unit tests for AddTaskCAERequest class focusing on new custProtectCode field
 * added between revisions 6d55721 and 367b8b2
 */
public class AddTaskCAERequestTest {

    /**
     * Test custProtectCode getter method
     */
    public void testGetCustProtectCode() {
        System.out.println("Testing getCustProtectCode method...");
        
        try {
            AddTaskCAERequest request = new AddTaskCAERequest();
            
            // Test initial value (should be null)
            String initialValue = request.getCustProtectCode();
            if (initialValue == null) {
                System.out.println("✓ Initial custProtectCode value is null as expected");
            } else {
                System.err.println("✗ Initial custProtectCode value should be null, but was: " + initialValue);
            }
            
        } catch (Exception e) {
            System.err.println("✗ getCustProtectCode test failed: " + e.getMessage());
        }
    }

    /**
     * Test custProtectCode setter method
     */
    public void testSetCustProtectCode() {
        System.out.println("Testing setCustProtectCode method...");
        
        try {
            AddTaskCAERequest request = new AddTaskCAERequest();
            String testValue = "PROTECT001";
            
            // Set value
            request.setCustProtectCode(testValue);
            
            // Verify value was set
            String retrievedValue = request.getCustProtectCode();
            if (testValue.equals(retrievedValue)) {
                System.out.println("✓ setCustProtectCode correctly sets the value");
            } else {
                System.err.println("✗ setCustProtectCode failed. Expected: " + testValue + ", Got: " + retrievedValue);
            }
            
        } catch (Exception e) {
            System.err.println("✗ setCustProtectCode test failed: " + e.getMessage());
        }
    }

    /**
     * Test custProtectCode with null value
     */
    public void testSetCustProtectCode_NullValue() {
        System.out.println("Testing setCustProtectCode with null value...");
        
        try {
            AddTaskCAERequest request = new AddTaskCAERequest();
            
            // Set null value
            request.setCustProtectCode(null);
            
            // Verify null value was set
            String retrievedValue = request.getCustProtectCode();
            if (retrievedValue == null) {
                System.out.println("✓ setCustProtectCode correctly handles null value");
            } else {
                System.err.println("✗ setCustProtectCode with null failed. Expected: null, Got: " + retrievedValue);
            }
            
        } catch (Exception e) {
            System.err.println("✗ setCustProtectCode null test failed: " + e.getMessage());
        }
    }

    /**
     * Test custProtectCode with empty string
     */
    public void testSetCustProtectCode_EmptyString() {
        System.out.println("Testing setCustProtectCode with empty string...");
        
        try {
            AddTaskCAERequest request = new AddTaskCAERequest();
            String emptyValue = "";
            
            // Set empty value
            request.setCustProtectCode(emptyValue);
            
            // Verify empty value was set
            String retrievedValue = request.getCustProtectCode();
            if (emptyValue.equals(retrievedValue)) {
                System.out.println("✓ setCustProtectCode correctly handles empty string");
            } else {
                System.err.println("✗ setCustProtectCode with empty string failed. Expected: '', Got: " + retrievedValue);
            }
            
        } catch (Exception e) {
            System.err.println("✗ setCustProtectCode empty string test failed: " + e.getMessage());
        }
    }

    /**
     * Test JSON serialization of custProtectCode field
     */
    public void testCustProtectCode_JsonSerialization() {
        System.out.println("Testing custProtectCode JSON serialization...");
        
        try {
            AddTaskCAERequest request = new AddTaskCAERequest();
            request.setCustProtectCode("PROTECT123");
            
            Gson gson = new Gson();
            String json = gson.toJson(request);
            
            // Check if custProtectCode is present in JSON
            if (json.contains("custProtectCode") && json.contains("PROTECT123")) {
                System.out.println("✓ custProtectCode correctly serialized to JSON");
            } else {
                System.err.println("✗ custProtectCode not found in JSON serialization");
                System.err.println("JSON: " + json.substring(0, Math.min(200, json.length())) + "...");
            }
            
        } catch (Exception e) {
            System.err.println("✗ JSON serialization test failed: " + e.getMessage());
        }
    }

    /**
     * Test JSON deserialization of custProtectCode field
     */
    public void testCustProtectCode_JsonDeserialization() {
        System.out.println("Testing custProtectCode JSON deserialization...");
        
        try {
            String json = "{\"custProtectCode\":\"PROTECT456\"}";
            
            Gson gson = new Gson();
            AddTaskCAERequest request = gson.fromJson(json, AddTaskCAERequest.class);
            
            String deserializedValue = request.getCustProtectCode();
            if ("PROTECT456".equals(deserializedValue)) {
                System.out.println("✓ custProtectCode correctly deserialized from JSON");
            } else {
                System.err.println("✗ custProtectCode deserialization failed. Expected: PROTECT456, Got: " + deserializedValue);
            }
            
        } catch (Exception e) {
            System.err.println("✗ JSON deserialization test failed: " + e.getMessage());
        }
    }

    /**
     * Test JSON property annotation
     */
    public void testCustProtectCode_JsonPropertyAnnotation() {
        System.out.println("Testing custProtectCode JsonProperty annotation...");
        
        try {
            // Use reflection to check if the field has JsonProperty annotation
            java.lang.reflect.Field field = AddTaskCAERequest.class.getDeclaredField("custProtectCode");
            
            if (field != null) {
                // Check if JsonProperty annotation exists
                boolean hasJsonProperty = field.isAnnotationPresent(com.fasterxml.jackson.annotation.JsonProperty.class);
                
                if (hasJsonProperty) {
                    com.fasterxml.jackson.annotation.JsonProperty annotation = 
                        field.getAnnotation(com.fasterxml.jackson.annotation.JsonProperty.class);
                    String propertyName = annotation.value();
                    
                    if ("custProtectCode".equals(propertyName)) {
                        System.out.println("✓ custProtectCode has correct JsonProperty annotation");
                    } else {
                        System.err.println("✗ JsonProperty annotation has wrong value: " + propertyName);
                    }
                } else {
                    System.err.println("✗ custProtectCode field missing JsonProperty annotation");
                }
            } else {
                System.err.println("✗ custProtectCode field not found");
            }
            
        } catch (Exception e) {
            System.err.println("✗ JsonProperty annotation test failed: " + e.getMessage());
        }
    }

    /**
     * Test method existence and accessibility
     */
    public void testMethodExistence() {
        System.out.println("Testing custProtectCode method existence...");
        
        try {
            Class<?> clazz = AddTaskCAERequest.class;
            
            // Check getter method
            Method getter = clazz.getMethod("getCustProtectCode");
            if (getter != null && getter.getReturnType() == String.class) {
                System.out.println("✓ getCustProtectCode method exists with correct return type");
            } else {
                System.err.println("✗ getCustProtectCode method not found or wrong return type");
            }
            
            // Check setter method
            Method setter = clazz.getMethod("setCustProtectCode", String.class);
            if (setter != null && setter.getReturnType() == void.class) {
                System.out.println("✓ setCustProtectCode method exists with correct signature");
            } else {
                System.err.println("✗ setCustProtectCode method not found or wrong signature");
            }
            
        } catch (Exception e) {
            System.err.println("✗ Method existence test failed: " + e.getMessage());
        }
    }

    /**
     * Main method to run all tests
     */
    public static void main(String[] args) {
        AddTaskCAERequestTest test = new AddTaskCAERequestTest();
        
        System.out.println("=== AddTaskCAERequest custProtectCode Test Suite ===");
        System.out.println("Testing new custProtectCode field added between revisions 6d55721 and 367b8b2\n");
        
        test.testGetCustProtectCode();
        test.testSetCustProtectCode();
        test.testSetCustProtectCode_NullValue();
        test.testSetCustProtectCode_EmptyString();
        test.testCustProtectCode_JsonSerialization();
        test.testCustProtectCode_JsonDeserialization();
        test.testCustProtectCode_JsonPropertyAnnotation();
        test.testMethodExistence();
        
        System.out.println("\n=== AddTaskCAERequest Test Suite Complete ===");
    }
}
